//@version=6
strategy("ADX Crossover Strategy with RSI", overlay=true)

// Trade Direction Setting
tradeDirection = input.string("Both", title="Trade Direction", options=["Long Only", "Short Only", "Both"])

// Time Filter Settings
useTimeFilter = input.bool(true, title="Use Time Filter")
tradingSession1 = input.session("0915-1130", title="Trading Session 1")
tradingSession2 = input.session("1315-1530", title="Trading Session 2")

// RSI Parameters
rsiLength = input.int(14, title="RSI Length", minval=1)
rsiLevel = input.float(50, title="RSI Level", minval=0, maxval=100)

// ADX Parameters
adxLength = input.int(14, title="ADX Length", minval=1)
adxThreshold = input.float(20, title="ADX Crossover Level", minval=0, maxval=100)

// Target and Stop Loss Parameters (1% each)
targetPercent = input.float(1.0, title="Target Percentage", minval=0.1, maxval=10.0)
stopLossPercent = input.float(1.0, title="Stop Loss Percentage", minval=0.1, maxval=10.0)

// Trade Limit Settings
useDailyLimit = input.bool(true, title="Use Daily Trade Limit")
maxTradesPerDay = input.int(10, title="Max Trades Per Day", minval=1)

// Variables to store entry prices and trade count
var float entryPrice = na
var int dailyTradeCount = 0
var int lastTradeDay = 0
var bool tradeActive = false

// Calculate RSI
rsi = ta.rsi(close, rsiLength)

// Calculate ADX manually
up = ta.change(high)
down = -ta.change(low)
plusDM = na(up) ? na : (up > down and up > 0 ? up : 0)
minusDM = na(down) ? na : (down > up and down > 0 ? down : 0)
truerange = ta.rma(ta.tr, adxLength)
plus = fixnan(100 * ta.rma(plusDM, adxLength) / truerange)
minus = fixnan(100 * ta.rma(minusDM, adxLength) / truerange)
sum = plus + minus
adx = 100 * ta.rma(math.abs(plus - minus) / (sum == 0 ? 1 : sum), adxLength)

// Check for ADX crossover above threshold
adxCrossover = ta.crossover(adx, adxThreshold)

// Time filter logic
inSession1 = time(timeframe.period, tradingSession1)
inSession2 = time(timeframe.period, tradingSession2)
inTimeRange = not useTimeFilter or (not na(inSession1) or not na(inSession2))

// Check if we just exited both sessions (time to close positions)
wasInSession1 = not na(inSession1[1])
wasInSession2 = not na(inSession2[1])
nowOutOfBothSessions = na(inSession1) and na(inSession2)
timeToClose = useTimeFilter and nowOutOfBothSessions and (wasInSession1 or wasInSession2)

// Daily trade limit logic
currentDay = dayofweek
if currentDay != lastTradeDay
    dailyTradeCount := 0
    lastTradeDay := currentDay

canTrade = not useDailyLimit or dailyTradeCount < maxTradesPerDay

// Entry conditions - ADX crossover with RSI filter
longCondition = adxCrossover and rsi > rsiLevel and strategy.position_size == 0 and not tradeActive and inTimeRange and canTrade
shortCondition = adxCrossover and rsi < rsiLevel and strategy.position_size == 0 and not tradeActive and inTimeRange and canTrade

// Execute trades based on trade direction setting
if longCondition and (tradeDirection == "Long Only" or tradeDirection == "Both")
    entryPrice := close
    strategy.entry("Long", strategy.long)
    dailyTradeCount := dailyTradeCount + 1
    tradeActive := true

if shortCondition and (tradeDirection == "Short Only" or tradeDirection == "Both")
    entryPrice := close
    strategy.entry("Short", strategy.short)
    dailyTradeCount := dailyTradeCount + 1
    tradeActive := true

// Calculate target and stop loss levels
targetLong = entryPrice * (1 + targetPercent / 100)
stopLossLong = entryPrice * (1 - stopLossPercent / 100)
targetShort = entryPrice * (1 - targetPercent / 100)
stopLossShort = entryPrice * (1 + stopLossPercent / 100)

// Exit trades based on 1% target and stop loss
if strategy.position_size > 0 and tradeActive
    if close >= targetLong or close <= stopLossLong or timeToClose
        strategy.close("Long")
        entryPrice := na
        tradeActive := false

if strategy.position_size < 0 and tradeActive
    if close <= targetShort or close >= stopLossShort or timeToClose
        strategy.close("Short")
        entryPrice := na
        tradeActive := false

// Force close all positions when end time is reached
if timeToClose and strategy.position_size != 0
    strategy.close_all(comment="Time Filter Close")
    entryPrice := na

// Plot Heikin Ashi candles
plotcandle(ha_open, ha_high, ha_low, ha_close, "Heikin Ashi Candles", color = isGreenCandle ? color.green : color.red)

// Plot RSI
plot(rsi, "RSI", color=color.blue, linewidth=2)

// Plot ADX
plot(adx, "ADX", color=color.purple, linewidth=2)
hline(adxThreshold, "ADX Crossover Level", color=color.gray, linestyle=hline.style_dashed)

// Plot crossover signals
plotshape(adxCrossover, title="ADX Crossover", location=location.belowbar, color=color.yellow, style=shape.triangleup, size=size.small)
bgcolor(adxCrossover ? color.new(color.yellow, 80) : na, title="ADX Crossover Signal")
